<template>
  <div class="course-detail-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <button
        class="back-to-courses-btn"
        @click="handleBackToCourses"
        type="button"
      >
        <i class="back-icon"></i>
        <span>返回课程列表</span>
      </button>
      <div class="header-content">
        <h1 class="page-title">{{ courseInfo.title || '课程详情' }}</h1>
        <p class="course-subtitle">{{ courseInfo.semester }} · {{ courseInfo.studentCount }}名学生</p>
      </div>
    </div>

    <!-- 主要内容布局 -->
    <div class="content-layout">
      <!-- 左侧导航菜单 -->
      <div class="sidebar">
        <div class="sidebar-menu">
          <div
            v-for="tab in menuTabs"
            :key="tab.key"
            class="menu-item"
            :class="{ active: activeTab === tab.key }"
            @click="switchTab(tab.key)"
          >
            <i class="menu-icon" :class="tab.icon"></i>
            <span class="menu-text">{{ tab.label }}</span>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 章节管理模块 -->
        <CourseChapters
          v-if="activeTab === 'chapters'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />

        <!-- 知识图谱模块 -->
        <CourseKnowledgeGraph
          v-if="activeTab === 'knowledge-graph'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />

        <!-- 作业管理模块 -->
        <CourseHomework
          v-if="activeTab === 'homework'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />

        <!-- 讨论区模块 -->
        <CourseDiscussion
          v-if="activeTab === 'discussion'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />

        <!-- 课程资料模块 -->
        <CourseMaterials
          v-if="activeTab === 'materials'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />

        <!-- 学情分析模块 -->
        <CourseAnalytics
          v-if="activeTab === 'analytics'"
          :course-id="courseId"
          @refresh="handleRefresh"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 导入子组件
import CourseChapters from './CourseChapters.vue';
import CourseKnowledgeGraph from './CourseKnowledgeGraph.vue';
import CourseHomework from './CourseHomework.vue';
import CourseDiscussion from './CourseDiscussion.vue';
import CourseMaterials from './CourseMaterials.vue';
import CourseAnalytics from './CourseAnalytics.vue';

const route = useRoute();
const router = useRouter();

// 当前激活的标签页
const activeTab = ref('chapters');

// 课程ID（从路由参数获取）
const courseId = computed(() => route.params.courseId);

// 课程信息
const courseInfo = ref({
  title: '',
  semester: '',
  studentCount: 0,
  totalHours: 0,
  progress: 0
});

// 菜单标签页配置
const menuTabs = [
  {
    key: 'chapters',
    label: '章节管理',
    icon: 'chapters-icon'
  },
  {
    key: 'knowledge-graph',
    label: '知识图谱',
    icon: 'knowledge-graph-icon'
  },
  {
    key: 'homework',
    label: '作业管理',
    icon: 'homework-icon'
  },
  {
    key: 'discussion',
    label: '讨论区',
    icon: 'discussion-icon'
  },
  {
    key: 'materials',
    label: '课程资料',
    icon: 'materials-icon'
  },
  {
    key: 'analytics',
    label: '学情分析',
    icon: 'analytics-icon'
  }
];

// 切换标签页
const switchTab = (tabKey) => {
  activeTab.value = tabKey;
  // 保存当前标签页到本地存储
  localStorage.setItem(`course-detail-active-tab-${courseId.value}`, tabKey);
  console.log('切换到标签页:', tabKey);
};

// 返回课程列表
const handleBackToCourses = () => {
  router.push('/teacher/my');
  console.log('返回课程列表');
};

// 刷新处理
const handleRefresh = () => {
  console.log('刷新课程详情数据');
  loadCourseInfo();
};

// 加载课程信息
const loadCourseInfo = async () => {
  try {
    // TODO: 实际的API调用
    // const response = await getCourseDetail(courseId.value);
    // courseInfo.value = response.data;

    // 模拟数据
    courseInfo.value = {
      title: '高等数学A',
      semester: '2024春季学期',
      studentCount: 45,
      totalHours: 64,
      progress: 75
    };
  } catch (error) {
    console.error('加载课程信息失败:', error);
  }
};

// 组件挂载时的初始化
onMounted(() => {
  // 从本地存储恢复上次的标签页
  const savedTab = localStorage.getItem(`course-detail-active-tab-${courseId.value}`);
  if (savedTab && menuTabs.some(tab => tab.key === savedTab)) {
    activeTab.value = savedTab;
  }

  // 加载课程信息
  loadCourseInfo();

  console.log('课程详情页面已加载，课程ID:', courseId.value, '当前标签页:', activeTab.value);
});
</script>

<style scoped>
/* 课程详情页面样式 */
.course-detail-container {
  min-height: 100vh;
  background-color: var(--background-color-secondary, #f9fafb);
  font-family: var(--font-family-sans, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif);
}

/* 页面头部样式 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.75rem 2rem;
  text-align: center;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.back-to-courses-btn {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.back-to-courses-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%) translateX(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.back-icon {
  position: relative;
  width: 12px;
  height: 12px;
}

.back-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  height: 10px;
  border-left: 2px solid white;
  border-bottom: 2px solid white;
  transform: translateY(-50%) rotate(45deg);
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.course-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
  margin: 0.25rem 0 0 0;
}

/* 内容布局样式 */
.content-layout {
  display: flex;
  min-height: calc(100vh - 100px);
  background-color: var(--background-color, #ffffff);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
}

/* 侧边栏样式 */
.sidebar {
  width: 200px;
  background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
  border-right: 1px solid var(--border-color, #e5e7eb);
  flex-shrink: 0;
}

.sidebar-menu {
  padding: 1rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  color: var(--text-color-secondary, #6b7280);
  font-weight: 500;
}

.menu-item:hover {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--primary-color, #6366f1);
  border-left-color: var(--primary-color, #6366f1);
}

.menu-item.active {
  background-color: rgba(99, 102, 241, 0.15);
  color: var(--primary-color, #6366f1);
  border-left-color: var(--primary-color, #6366f1);
  font-weight: 600;
}

.menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.menu-text {
  font-size: 0.875rem;
  white-space: nowrap;
}

/* 主内容区域样式 */
.main-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background-color: var(--background-color-secondary, #f9fafb);
}

/* 图标样式 */
.chapters-icon::before {
  content: '📚';
  font-size: 16px;
}

.knowledge-graph-icon::before {
  content: '🕸️';
  font-size: 16px;
}

.homework-icon::before {
  content: '📝';
  font-size: 16px;
}

.discussion-icon::before {
  content: '💬';
  font-size: 16px;
}

.materials-icon::before {
  content: '📁';
  font-size: 16px;
}

.analytics-icon::before {
  content: '📊';
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .sidebar-menu {
    display: flex;
    overflow-x: auto;
    padding: 0.5rem;
  }

  .menu-item {
    flex-shrink: 0;
    border-left: none;
    border-bottom: 3px solid transparent;
    min-width: 120px;
    justify-content: center;
  }

  .menu-item.active {
    border-left: none;
    border-bottom-color: var(--primary-color, #6366f1);
  }

  .main-content {
    order: 1;
    padding: 1rem;
  }

  .back-to-courses-btn {
    position: static;
    transform: none;
    margin-bottom: 0.5rem;
  }

  .page-header {
    text-align: left;
    padding: 1rem;
  }
}
</style>