import request from './axios';

/**
 * 获取班级列表（使用部门接口）
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 班级列表响应数据
 */
export const getClassesList = (deptId,params = {}) => {
  

  // 构建查询参数，过滤掉空值
  const cleanParams = {};
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
      cleanParams[key] = params[key];
    }
  });

  return request({
    url: '/system/dept/list/include/'+deptId,
    method: 'get',
    params: cleanParams,
  }).then(response => {
    console.log('获取班级列表成功:', response);


      // 标准AjaxResult格式：{ error, success, warn, empty, data }

        console.log('返回标准AjaxResult格式，success:', response.success);

        if (response.data) {
          // 如果data是数组，包装成TableDataInfo格式
            return {
              total: response.data.length,
              rows: response.data,
              code: response.code,
              msg: response.msg || '成功'
            };
        }else{
          return {
            total: 0,
            rows: [],
            code: -1,
            msg: response.msg || '获取班级列表失败'
          }
        }


    })}




/**
 * 创建新班级（使用部门接口）
 * @param {Object} classData 班级数据
 * @param {string} classData.name 班级名称
 * @returns {Promise<Object>} 创建结果
 */
export const createClass = (classData) => {
  console.log('正在创建班级...', classData);

  // 构建新的请求数据格式
  const requestData = {
    parentId: 101,
    deptName: classData.name || '',
    orderNum: 0
  };

  console.log('发送到部门接口的数据:', requestData);

  return request({
    url: '/system/dept',
    method: 'post',
    data: requestData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建班级API原始响应:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式，success:', response.success);
        console.log('响应数据data:', response.data);

        const result = {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '创建成功' : '创建失败'),
          data: response.data || null
        };

        console.log('处理后的响应结果:', result);
        return result;
      }
    }

    console.warn('响应格式不符合预期，原始响应:', response);
    console.log('响应类型:', typeof response);

    // 如果响应是数字，可能直接是班级ID
    if (typeof response === 'number') {
      console.log('响应是数字，可能是班级ID:', response);
      return {
        success: true,
        error: false,
        warn: false,
        empty: false,
        msg: '创建成功',
        data: response
      };
    }

    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '创建成功',
      data: response
    };
  }).catch(error => {
    console.error('创建班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '创建班级失败',
      data: null
    };
  });
};

/**
 * 根据ID获取班级详情（使用部门接口）
 * @param {string|number} classId 班级ID
 * @returns {Promise<Object>} 班级详情数据
 */
export const getClassById = (classId) => {
  console.log(`正在获取班级详情: ${classId}`);

  return request({
    url: `/system/dept/${classId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log(`获取班级${classId}详情成功:`, response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '获取成功' : '获取失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '获取成功',
      data: response
    };
  }).catch(error => {
    console.error(`获取班级 ${classId} 详情失败:`, error);
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '获取班级详情失败',
      data: null
    };
  });
};

/**
 * 更新班级信息
 * @param {Object} classData 班级数据
 * @param {string} classData.createBy 创建者
 * @param {string} classData.createTime 创建时间
 * @param {string} classData.updateBy 更新者
 * @param {string} classData.updateTime 更新时间
 * @param {string} classData.remark 备注
 * @param {Object} classData.params 其他参数
 * @param {number} classData.id 班级ID
 * @param {string} classData.name 班级名称
 * @returns {Promise<Object>} 更新结果
 */
export const updateClass = (classData) => {
  console.log('正在更新班级...', classData);

  // 验证必填字段
  if (!classData.id || classData.id === 0) {
    console.error('更新班级失败: 班级ID不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '班级ID不能为空',
      data: null
    });
  }

  if (!classData.name || !classData.name.trim()) {
    console.error('更新班级失败: 班级名称不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '班级名称不能为空',
      data: null
    });
  }

  // 构建符合API要求的数据格式
  const requestData = {
    id: classData.id,
    name: classData.name.trim(),
    createBy: classData.createBy || '',
    createTime: classData.createTime || '',
    updateBy: classData.updateBy || '',
    updateTime: classData.updateTime || new Date().toISOString(),
    remark: classData.remark || '',
    params: classData.params || {}
  };

  console.log('发送的更新数据:', requestData);

  return request({
    url: '/system/dept',
    method: 'put',
    data: requestData,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('更新班级成功:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '更新成功' : '更新失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '更新成功',
      data: response
    };
  }).catch(error => {
    console.error('更新班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '更新班级失败',
      data: null
    };
  });
};

/**
 * 删除班级
 * @param {Array<number>} ids 要删除的班级ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const deleteClasses = (ids) => {
  console.log('正在删除班级...', ids);

  // 验证参数
  if (!Array.isArray(ids) || ids.length === 0) {
    console.error('删除班级失败: 班级ID数组不能为空');
    return Promise.resolve({
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: '班级ID数组不能为空',
      data: null
    });
  }

  // 将数组转换为逗号分隔的字符串，符合路径参数格式
  const idsString = ids.join(',');

  return request({
    url: `/system/dept/${idsString}`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('删除班级成功:', response);

    // 检查响应格式
    if (response && typeof response === 'object') {
      // 标准响应格式：{ error, success, warn, empty }
      if (response.hasOwnProperty('success')) {
        console.log('返回标准AjaxResult格式');
        return {
          success: response.success,
          error: response.error || false,
          warn: response.warn || false,
          empty: response.empty || false,
          msg: response.msg || (response.success ? '删除成功' : '删除失败'),
          data: response.data || null
        };
      }
    }

    console.warn('响应格式不符合预期，返回默认成功结果');
    return {
      success: true,
      error: false,
      warn: false,
      empty: false,
      msg: '删除成功',
      data: response
    };
  }).catch(error => {
    console.error('删除班级失败:', error);
    // 返回错误格式，保持与AjaxResult一致
    return {
      success: false,
      error: true,
      warn: false,
      empty: false,
      msg: error.message || '删除班级失败',
      data: null
    };
  });


};
  